import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:dasso_reader/config/design_system.dart';

/// Specialized extensions for the DesignSystem to handle specific UI patterns
/// while maintaining consistency with the core design system.
///
/// This approach allows for:
/// - Specialized constants for different app sections
/// - Consistent base values from core DesignSystem
/// - Easy maintenance and updates
/// - Type-safe design tokens

/// Settings Page Design Extensions
///
/// Provides specialized design tokens for settings pages while maintaining
/// consistency with the core DesignSystem.
class SettingsDesign {
  SettingsDesign._();

  // =====================================================
  // SETTINGS SPECIFIC CONSTANTS
  // =====================================================

  /// Settings page header height
  static const double headerHeight = 120.0;

  /// Settings section header height
  static const double sectionHeaderHeight = 48.0;

  /// Settings item minimum height for accessibility
  static const double itemMinHeight = DesignSystem.widgetMinTouchTarget;

  /// Settings switch/toggle size
  static const double toggleSize = 24.0;

  /// Settings icon container size
  static const double iconContainerSize = 40.0;

  // =====================================================
  // SETTINGS SPECIFIC PADDING & MARGINS
  // =====================================================

  /// Page-level padding for settings screens (use getPagePadding method instead)
  static const EdgeInsets pagePadding = EdgeInsets.all(DesignSystem.spaceM);

  /// Section divider margin
  static const EdgeInsets sectionDividerMargin = EdgeInsets.symmetric(
    vertical: DesignSystem.spaceL,
  );

  /// Settings group card padding
  static const EdgeInsets groupCardPadding =
      EdgeInsets.all(DesignSystem.spaceM);

  /// Individual setting item padding
  static const EdgeInsets itemPadding = EdgeInsets.symmetric(
    horizontal: DesignSystem.spaceM,
    vertical: DesignSystem.spaceS,
  );

  // =====================================================
  // ADAPTIVE METHODS FOR SETTINGS
  // =====================================================

  /// Returns adaptive page padding for settings
  static EdgeInsets getPagePadding(BuildContext context) {
    return DesignSystem.getAdaptivePadding(context);
  }

  /// Returns adaptive card width for settings groups
  static double getCardWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (DesignSystem.isDesktop(context)) {
      return (screenWidth * 0.7).clamp(400.0, 800.0);
    } else if (DesignSystem.isTablet(context)) {
      return screenWidth - (DesignSystem.spaceL * 2);
    } else {
      return screenWidth - (DesignSystem.spaceM * 2);
    }
  }

  /// Returns adaptive list tile height for settings
  static double getListTileHeight(BuildContext context) {
    return DesignSystem.getAdaptiveListTileHeight(context);
  }
}

/// Reading Experience Design Extensions
///
/// Specialized design tokens for reading interface while maintaining
/// consistency with core DesignSystem.
class ReadingDesign {
  ReadingDesign._();

  // =====================================================
  // READING SPECIFIC CONSTANTS
  // =====================================================

  /// Reading controls animation duration
  static const Duration controlsAnimationDuration = DesignSystem.durationMedium;

  /// Reading overlay fade duration
  static const Duration overlayFadeDuration = DesignSystem.durationFast;

  /// Reading menu border radius
  static const double menuBorderRadius = DesignSystem.radiusL;

  /// Reading progress bar height
  static const double progressBarHeight = 6.0;

  /// Reading controls minimum height
  static const double controlsMinHeight = 56.0;

  // =====================================================
  // READING SPECIFIC PADDING & MARGINS
  // =====================================================

  /// Reading controls container padding
  static const EdgeInsets controlsContainerPadding = EdgeInsets.symmetric(
    horizontal: DesignSystem.spaceM,
    vertical: DesignSystem.spaceS,
  );

  /// Reading menu item padding
  static const EdgeInsets menuItemPadding = EdgeInsets.symmetric(
    horizontal: DesignSystem.spaceM,
    vertical: DesignSystem.spaceS,
  );

  /// Reading toolbar padding
  static const EdgeInsets toolbarPadding = EdgeInsets.symmetric(
    horizontal: DesignSystem.spaceM,
  );

  // =====================================================
  // ADAPTIVE METHODS FOR READING
  // =====================================================

  /// Returns adaptive controls height based on screen size
  static double getControlsHeight(BuildContext context) {
    if (DesignSystem.isDesktop(context)) {
      return 64.0;
    } else if (DesignSystem.isTablet(context)) {
      return 56.0;
    } else {
      return 48.0;
    }
  }

  /// Returns adaptive menu width for reading controls
  static double getMenuWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (DesignSystem.isDesktop(context)) {
      return (screenWidth * 0.3).clamp(300.0, 500.0);
    } else if (DesignSystem.isTablet(context)) {
      return (screenWidth * 0.5).clamp(250.0, 400.0);
    } else {
      return (screenWidth * 0.8).clamp(200.0, 350.0);
    }
  }
}

/// Widget Components Design Extensions
///
/// Specialized design tokens for reusable widget components.
class WidgetDesign {
  WidgetDesign._();

  // =====================================================
  // WIDGET SPECIFIC CONSTANTS
  // =====================================================

  /// Standard card elevation for widgets
  static const double cardElevation = DesignSystem.elevationS;

  /// Widget loading indicator size
  static const double loadingIndicatorSize = 24.0;

  /// Widget avatar size (small)
  static const double avatarSizeSmall = 32.0;

  /// Widget avatar size (medium)
  static const double avatarSizeMedium = 48.0;

  /// Widget avatar size (large)
  static const double avatarSizeLarge = 64.0;

  // =====================================================
  // WIDGET SPECIFIC PADDING & MARGINS
  // =====================================================

  /// Standard widget card padding
  static const EdgeInsets cardPadding = EdgeInsets.all(DesignSystem.spaceM);

  /// Widget list item padding
  static const EdgeInsets listItemPadding = EdgeInsets.symmetric(
    horizontal: DesignSystem.spaceM,
    vertical: DesignSystem.spaceS,
  );

  /// Widget button padding
  static const EdgeInsets buttonPadding = EdgeInsets.symmetric(
    horizontal: DesignSystem.spaceL,
    vertical: DesignSystem.spaceS,
  );

  // =====================================================
  // ADAPTIVE METHODS FOR WIDGETS
  // =====================================================

  /// Returns adaptive card margin based on screen size
  static EdgeInsets getCardMargin(BuildContext context) {
    if (DesignSystem.isDesktop(context)) {
      return const EdgeInsets.all(DesignSystem.spaceL);
    } else if (DesignSystem.isTablet(context)) {
      return const EdgeInsets.all(DesignSystem.spaceM);
    } else {
      return const EdgeInsets.all(DesignSystem.spaceS);
    }
  }

  /// Returns adaptive icon size for widgets
  static double getIconSize(BuildContext context, {bool isLarge = false}) {
    final baseSize = isLarge
        ? DesignSystem.widgetIconSizeLarge
        : DesignSystem.widgetIconSizeMedium;

    return DesignSystem.getAdaptiveIconSize(context, baseSize);
  }
}

/// Status Bar Design Extensions
///
/// Professional status bar styling that integrates with the app's theme system
/// and provides consistent appearance across all Android manufacturers.
///
/// This extension maintains consistency with our DesignSystem while providing
/// specialized status bar functionality for different app contexts.
class StatusBarDesign {
  StatusBarDesign._();

  // =====================================================
  // STATUS BAR STYLE CONSTANTS
  // =====================================================

  /// Light theme status bar style - optimized for light backgrounds
  /// Uses transparent status bar with dark icons for maximum compatibility
  static const SystemUiOverlayStyle lightStatusBar = SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    statusBarIconBrightness: Brightness.dark,
    statusBarBrightness: Brightness.light,
    systemNavigationBarColor: Colors.transparent,
    systemNavigationBarIconBrightness: Brightness.dark,
  );

  /// Dark theme status bar style - optimized for dark backgrounds
  /// Uses transparent status bar with light icons for dark themes
  static const SystemUiOverlayStyle darkStatusBar = SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    statusBarIconBrightness: Brightness.light,
    statusBarBrightness: Brightness.dark,
    systemNavigationBarColor: Colors.transparent,
    systemNavigationBarIconBrightness: Brightness.light,
  );

  /// Reading mode status bar style - minimal distraction for reading
  /// Optimized for reading experience with subtle styling
  static const SystemUiOverlayStyle readingModeStatusBar = SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    statusBarIconBrightness: Brightness.dark,
    statusBarBrightness: Brightness.light,
    systemNavigationBarColor: Colors.transparent,
    systemNavigationBarIconBrightness: Brightness.dark,
  );

  /// Immersive reading status bar style - for full-screen reading
  /// Used when status bar is hidden or in immersive mode
  static const SystemUiOverlayStyle immersiveStatusBar = SystemUiOverlayStyle(
    statusBarColor: Colors.black,
    statusBarIconBrightness: Brightness.light,
    statusBarBrightness: Brightness.dark,
    systemNavigationBarColor: Colors.black,
    systemNavigationBarIconBrightness: Brightness.light,
  );

  // =====================================================
  // ADAPTIVE STATUS BAR METHODS
  // =====================================================

  /// Returns the appropriate status bar style based on current theme
  ///
  /// Automatically adapts to light/dark theme and provides consistent
  /// status bar appearance across the entire app.
  static SystemUiOverlayStyle getAdaptiveStyle(BuildContext context) {
    final brightness = Theme.of(context).brightness;
    return brightness == Brightness.dark ? darkStatusBar : lightStatusBar;
  }

  /// Returns status bar style optimized for reading experience
  ///
  /// [isImmersive] - Whether to use full immersive mode
  /// [backgroundColor] - Optional background color to optimize icon visibility
  static SystemUiOverlayStyle getReadingStyle(
    BuildContext context, {
    required bool isImmersive,
    Color? backgroundColor,
  }) {
    if (isImmersive) {
      return immersiveStatusBar;
    }

    // Determine icon brightness based on background color
    if (backgroundColor != null) {
      final luminance = backgroundColor.computeLuminance();

      // For light backgrounds (luminance > 0.5): use dark icons (black)
      // For dark backgrounds (luminance ≤ 0.5): use light icons (white)
      final usesDarkIcons = luminance > 0.5;

      // Create explicit style for better Android compatibility
      final style = SystemUiOverlayStyle(
        statusBarColor: backgroundColor,
        statusBarIconBrightness:
            usesDarkIcons ? Brightness.dark : Brightness.light,
        statusBarBrightness: usesDarkIcons ? Brightness.light : Brightness.dark,
        systemNavigationBarColor: backgroundColor,
        systemNavigationBarIconBrightness:
            usesDarkIcons ? Brightness.dark : Brightness.light,
      );

      return style;
    }

    return getAdaptiveStyle(context);
  }

  /// Applies status bar style with proper error handling
  ///
  /// Safely applies the status bar style with graceful fallback
  /// if the platform doesn't support the styling.
  static void applyStyle(SystemUiOverlayStyle style) {
    try {
      SystemChrome.setSystemUIOverlayStyle(style);
    } catch (e) {
      // Graceful fallback - continue without status bar styling
      debugPrint('StatusBarDesign: Failed to apply style - $e');
    }
  }

  /// Sets status bar style based on app theme automatically
  ///
  /// Convenience method that applies the appropriate style for the current theme.
  /// Should be called when the app starts or theme changes.
  static void applyAdaptiveStyle(BuildContext context) {
    applyStyle(getAdaptiveStyle(context));
  }

  /// Sets status bar style for reading experience
  ///
  /// Specialized method for reading interface that considers immersive mode
  /// and background colors for optimal reading experience.
  static void applyReadingStyle(
    BuildContext context, {
    required bool isImmersive,
    Color? backgroundColor,
  }) {
    applyStyle(getReadingStyle(
      context,
      isImmersive: isImmersive,
      backgroundColor: backgroundColor,
    ));
  }
}
