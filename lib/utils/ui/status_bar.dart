import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:dasso_reader/config/design_system_extensions.dart';

/// Enhanced status bar utilities with DesignSystem integration
///
/// Provides both functional control (show/hide) and styling (theme-aware)
/// for a professional, consistent status bar experience across all devices.
///
/// This utility maintains backward compatibility while adding DesignSystem
/// integration for consistent theming across all Android manufacturers.

// =====================================================
// FUNCTIONAL STATUS BAR CONTROL (Existing functionality preserved)
// =====================================================

/// Hides the status bar completely for immersive experience
///
/// Used primarily in reading mode when user wants full-screen experience.
/// Preserves existing functionality while maintaining compatibility.
void hideStatusBar() {
  SystemChrome.setEnabledSystemUIMode(
    SystemUiMode.manual,
    overlays: [],
  );
}

/// Shows the status bar with all system overlays
///
/// Restores normal status bar visibility with all system UI elements.
/// Maintains existing behavior for backward compatibility.
void showStatusBar() {
  SystemChrome.setEnabledSystemUIMode(
    SystemUiMode.manual,
    overlays: SystemUiOverlay.values,
  );
}

/// Shows status bar without resizing the app content (edge-to-edge)
///
/// Enables edge-to-edge display while keeping status bar visible.
/// Used in reading mode for better content display.
void showStatusBarWithoutResize() {
  SystemChrome.setEnabledSystemUIMode(
    SystemUiMode.edgeToEdge,
  );
}

/// Shows only the status bar (no navigation bar)
///
/// Displays status bar while hiding navigation bar.
/// Useful for specific reading modes or immersive experiences.
void onlyStatusBar() {
  SystemChrome.setEnabledSystemUIMode(
    SystemUiMode.manual,
    overlays: [
      SystemUiOverlay.top,
    ],
  );
}

// =====================================================
// ENHANCED STATUS BAR STYLING (New DesignSystem integration)
// =====================================================

/// Applies theme-aware status bar styling
///
/// Automatically adapts status bar icons and colors based on the current theme.
/// Should be called when the app starts or theme changes.
///
/// This is the main method to use for consistent status bar theming.
void applyAdaptiveStatusBarStyle(BuildContext context) {
  StatusBarDesign.applyAdaptiveStyle(context);
}

/// Applies status bar styling optimized for reading experience
///
/// [isImmersive] - Whether to use full immersive mode
/// [backgroundColor] - Optional background color to optimize icon visibility
///
/// Use this method in reading interfaces for optimal status bar appearance.
void applyReadingStatusBarStyle(
  BuildContext context, {
  required bool isImmersive,
  Color? backgroundColor,
}) {
  StatusBarDesign.applyReadingStyle(
    context,
    isImmersive: isImmersive,
    backgroundColor: backgroundColor,
  );
}

/// Shows status bar with proper theme-aware styling
///
/// Enhanced version of showStatusBar() that also applies appropriate styling
/// based on the current theme. Recommended for most use cases.
void showStyledStatusBar(BuildContext context) {
  showStatusBar();
  applyAdaptiveStatusBarStyle(context);
}

/// Shows status bar without resize and applies theme-aware styling
///
/// Enhanced version of showStatusBarWithoutResize() with styling.
/// Perfect for reading interfaces that need edge-to-edge content.
void showStyledStatusBarWithoutResize(BuildContext context) {
  showStatusBarWithoutResize();
  applyAdaptiveStatusBarStyle(context);
}

/// Hides status bar and applies immersive styling
///
/// Enhanced version of hideStatusBar() for reading mode.
/// Ensures proper styling when transitioning to/from hidden state.
void hideStatusBarForReading(BuildContext context) {
  hideStatusBar();
  applyReadingStatusBarStyle(context, isImmersive: true);
}
