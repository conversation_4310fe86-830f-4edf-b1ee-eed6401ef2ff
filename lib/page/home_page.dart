import 'dart:io';

import 'package:dasso_reader/enums/sync_direction.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/page/dictionary_page.dart';
import 'package:dasso_reader/page/home_page/bookshelf_page.dart';
import 'package:dasso_reader/page/home_page/hsk_page.dart';
import 'package:dasso_reader/page/home_page/notes_page.dart';
import 'package:dasso_reader/page/home_page/settings_page.dart';
import 'package:dasso_reader/page/home_page/vocabulary_page.dart';
import 'package:dasso_reader/service/convert_to_epub/txt/convert_from_text.dart';
import 'package:dasso_reader/widgets/add_book_menu.dart';
import 'package:dasso_reader/widgets/paste_text_fullscreen.dart';

import 'package:dasso_reader/service/book.dart';
import 'package:dasso_reader/utils/check_update.dart';
import 'package:dasso_reader/utils/get_path/get_temp_dir.dart';
import 'package:dasso_reader/utils/load_default_font.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/providers/anx_webdav.dart';
import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/utils/ui/status_bar.dart';
import 'package:dasso_reader/utils/toast/common.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:icons_plus/icons_plus.dart';
import 'package:receive_sharing_intent/receive_sharing_intent.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:file_picker/file_picker.dart';

WebViewEnvironment? webViewEnvironment;

class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _HomePageState();
}

// Using DesignSystem constants for consistent UI

class _HomePageState extends ConsumerState<HomePage>
    with SingleTickerProviderStateMixin {
  int _currentIndex = 0;
  bool? _expanded;
  late TabController _tabController;

  // Add search controller and state
  final TextEditingController _searchController = TextEditingController();
  bool _showSearch = false;

  // First, add a GlobalKey for the BookshelfPage
  final GlobalKey<BookshelfPageState> _bookshelfKey =
      GlobalKey<BookshelfPageState>();

  @override
  void initState() {
    super.initState();
    initAnx();

    // Initialize the tab controller with the correct number of tabs
    // Default: Bookshelf + Dictionary + Vocabulary + HSK
    int tabCount = 4; // Updated for 4 default tabs
    if (Prefs().bottomNavigatorShowNote) tabCount++;

    _tabController = TabController(length: tabCount, vsync: this);
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        setState(() {
          _currentIndex = _tabController.index;

          // Hide search when switching away from bookshelf tab
          if (_currentIndex != 0 && _showSearch) {
            _showSearch = false;
            _searchController.clear();
            final bookshelfState = _getBookshelfState();
            if (bookshelfState != null) {
              bookshelfState.searchBooks(null);
            }
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> initAnx() async {
    AnxToast.init(context);
    // Re-enabling update check, now from your fork
    checkUpdate(false);
    if (Prefs().webdavStatus) {
      await AnxWebdav().init();
      await AnxWebdav().syncData(SyncDirection.both, ref);
    }
    loadDefaultFont();

    if (defaultTargetPlatform == TargetPlatform.windows) {
      final availableVersion = await WebViewEnvironment.getAvailableVersion();
      AnxLog.info('WebView2 version: $availableVersion');

      if (availableVersion == null) {
        SmartDialog.show(
          builder: (context) => AlertDialog(
            title: const Icon(Icons.error),
            content: Text(L10n.of(context).webview2_not_installed),
            actions: [
              TextButton(
                onPressed: () => {
                  launchUrl(
                      Uri.parse(
                          'https://developer.microsoft.com/en-us/microsoft-edge/webview2'),
                      mode: LaunchMode.externalApplication)
                },
                child: Text(L10n.of(context).webview2_install),
              ),
            ],
          ),
        );
      } else {
        webViewEnvironment = await WebViewEnvironment.create(
          settings: WebViewEnvironmentSettings(
              userDataFolder: (await getAnxTempDir()).path),
        );
      }
    }

    if (Platform.isAndroid || Platform.isIOS) {
      // receive sharing intent
      Future<void> handleShare(List<SharedMediaFile> value) async {
        List<File> files = [];
        for (var item in value) {
          final sourceFile = File(item.path);
          files.add(sourceFile);
        }
        importBookList(files, context, ref);
        ReceiveSharingIntent.instance.reset();
      }

      ReceiveSharingIntent.instance.getMediaStream().listen((value) {
        AnxLog.info(
            'share: Receive share intent: ${value.map((e) => e.toMap())}');
        if (value.isNotEmpty) {
          handleShare(value);
        }
      }, onError: (err) {
        AnxLog.severe('share: Receive share intent');
      });

      ReceiveSharingIntent.instance.getInitialMedia().then((value) {
        AnxLog.info(
            'share: Receive share intent: ${value.map((e) => e.toMap())}');
        if (value.isNotEmpty) {
          handleShare(value);
        }
      }, onError: (err) {
        AnxLog.severe('share: Receive share intent');
      });
    }
  }

  // Method to show settings
  void _showSettings() {
    Navigator.push(
      context,
      CupertinoPageRoute(
        fullscreenDialog: false,
        builder: (context) => Scaffold(
          appBar: AppBar(
            title: const Text("Profile"),
          ),
          body: const SettingsPage(),
        ),
      ),
    );
  }

  // We'll keep this method for future implementation
  // void _showStatistics() {
  //   // Implementation removed as it's not currently used
  // }

  // Add a direct method for importing books
  Future<void> _importBook() async {
    // Directly call the import book functionality
    final result = await FilePicker.platform.pickFiles(
      type: FileType.any,
      allowMultiple: true,
    );

    if (result == null || result.files.isEmpty) {
      return;
    }

    List<PlatformFile> files = result.files;
    AnxLog.info('importBook files: ${files.toString()}');
    List<File> fileList = [];

    // FilePicker on Windows will return files with original path,
    // but on Android it will return files with temporary path.
    // So we need to save the files to the temp directory.
    if (!Platform.isAndroid) {
      fileList = await Future.wait(files.map((file) async {
        Directory tempDir = await getAnxTempDir();
        File tempFile = File('${tempDir.path}/${file.name}');
        await File(file.path!).copy(tempFile.path);
        return tempFile;
      }).toList());
    } else {
      fileList = files.map((file) => File(file.path!)).toList();
    }

    // Add mounted check to avoid using BuildContext across async gaps
    if (mounted) {
      importBookList(fileList, context, ref);
    }
  }

  // Method to paste text from clipboard
  Future<void> _pasteTextFromClipboard() async {
    await PasteTextFullScreen.show(
      context,
      // Disable auto-paste to start fresh each time
      autoPasteFromClipboard: false,
      onSave: (title, content) async {
        // Show loading indicator
        SmartDialog.showLoading(
          msg: 'Converting text...',
        );

        try {
          // Convert the text to an EPUB file
          final epubFile = await convertFromText(content, title);

          // Import the EPUB file
          if (mounted) {
            await importBook(epubFile, ref);
            AnxToast.show('Added "$title" to your bookshelf');
          }
        } catch (e) {
          AnxLog.severe('Error converting text to EPUB: $e');
          AnxToast.show('Failed to create book from text');
        } finally {
          SmartDialog.dismiss();
        }
      },
    );
  }

  // Show options menu for adding books
  void _showAddOptions() {
    showDialog(
      context: context,
      barrierDismissible:
          true, // Ensure the dialog can be dismissed by tapping outside
      builder: (context) => showAddBookMenu(
        context: context,
        onPasteText: _pasteTextFromClipboard,
        onImportFile: _importBook,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    // Apply adaptive status bar styling for home page
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        applyAdaptiveStatusBarStyle(context);
      }
    });

    // Create the list of pages
    List<Widget> pages = [
      BookshelfPage(key: _bookshelfKey),
      const DictionaryPage(), // Add Dictionary page
      const VocabularyPage(), // Add Vocabulary page
      const HskPage(), // Add HSK page
      if (Prefs().bottomNavigatorShowNote) const NotesPage(),
    ];

    // Create the tab items
    List<Tab> tabItems = [
      Tab(
        text: L10n.of(context).navBar_bookshelf,
        icon: const Icon(EvaIcons.book_open),
      ),
      Tab(
        text: 'Dictionary',
        icon: const Icon(Icons.translate),
      ),
      Tab(
        text: 'Vocabulary',
        icon: const Icon(Icons.menu_book_rounded),
      ),
      Tab(
        text: 'HSK',
        icon: const Icon(Icons.school_rounded),
      ),
      if (Prefs().bottomNavigatorShowNote)
        Tab(
          text: L10n.of(context).navBar_notes,
          icon: const Icon(Icons.note),
        ),
    ];

    // Use scaffold for responsive design
    return LayoutBuilder(
      builder: (context, constraints) {
        _expanded ??= constraints.maxWidth > 1000;

        // For desktop/large tablets
        if (constraints.maxWidth > 600) {
          return Scaffold(
            floatingActionButton: _currentIndex == 0
                ? FloatingActionButton(
                    onPressed: _showAddOptions,
                    tooltip: 'Add Book',
                    child: const Icon(Icons.add),
                  )
                : null,
            body: Row(
              children: [
                NavigationRail(
                  leading: Column(
                    children: [
                      // Avatar in the top of the rail
                      Padding(
                        padding:
                            const EdgeInsets.only(left: DesignSystem.spaceM),
                        child: IconButton.filledTonal(
                          tooltip: 'Settings',
                          onPressed: _showSettings,
                          icon: const Icon(Icons.person_outline),
                        ),
                      ),
                      SizedBox(
                          height: DesignSystem.spaceL), // Better visual rhythm
                      // Add app title for desktop layout
                      if (_expanded!)
                        Padding(
                          padding: const EdgeInsets.only(
                              bottom: DesignSystem.spaceM),
                          child: Text(
                            "DassoShu",
                            semanticsLabel: "DassoShu Reader App",
                            style: TextStyle(
                              fontSize: 22,
                              fontWeight: FontWeight.bold,
                              color: colorScheme.primary,
                              letterSpacing: 0.5,
                            ),
                          ),
                        ),
                      // Search button for desktop
                      if (_currentIndex == 0) // Only show for bookshelf tab
                        AnimatedSwitcher(
                          duration: DesignSystem.durationFast,
                          switchInCurve: Curves.easeInOut,
                          switchOutCurve: Curves.easeInOut,
                          child: _showSearch
                              ? Container(
                                  key: const ValueKey('search-expanded'),
                                  width: _expanded! ? 220 : 56,
                                  padding: EdgeInsets.symmetric(
                                      horizontal: DesignSystem.spaceS,
                                      vertical: DesignSystem.spaceXS),
                                  child: SearchBar(
                                    controller: _searchController,
                                    hintText:
                                        L10n.of(context).search_books_hint,
                                    hintStyle: WidgetStateProperty.all(
                                      TextStyle(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSurfaceVariant,
                                        fontSize: 14,
                                      ),
                                    ),
                                    textStyle: WidgetStateProperty.all(
                                      TextStyle(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSurface,
                                        fontSize: 14,
                                      ),
                                    ),
                                    backgroundColor:
                                        WidgetStateProperty.resolveWith<Color>(
                                            (states) {
                                      if (states
                                          .contains(WidgetState.focused)) {
                                        return Theme.of(context)
                                            .colorScheme
                                            .surfaceContainerLow;
                                      }
                                      return Theme.of(context)
                                          .colorScheme
                                          .surfaceContainerLowest;
                                    }),
                                    elevation: WidgetStateProperty.all(1.0),
                                    padding: const WidgetStatePropertyAll<
                                        EdgeInsets>(
                                      EdgeInsets.symmetric(horizontal: 8.0),
                                    ),
                                    constraints: BoxConstraints(
                                      minHeight: 40,
                                      maxHeight: 40,
                                    ),
                                    leading: Icon(
                                      Icons.search,
                                      size: 18,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onSurfaceVariant,
                                    ),
                                    trailing: [
                                      IconButton(
                                        icon: Icon(
                                          Icons.close,
                                          size: 16,
                                          color: Theme.of(context)
                                              .colorScheme
                                              .onSurfaceVariant,
                                        ),
                                        constraints: const BoxConstraints(
                                          minWidth: 24,
                                          minHeight: 24,
                                        ),
                                        padding: EdgeInsets.zero,
                                        onPressed: () {
                                          HapticFeedback.selectionClick();
                                          setState(() {
                                            _showSearch = false;
                                            _searchController.clear();
                                            // Clear search
                                            final bookshelfState =
                                                _getBookshelfState();
                                            if (bookshelfState != null) {
                                              bookshelfState.searchBooks(null);
                                            }
                                          });
                                        },
                                      ),
                                    ],
                                    onChanged: (value) {
                                      // Call search on each keystroke for immediate filtering
                                      final bookshelfState =
                                          _getBookshelfState();
                                      if (bookshelfState != null) {
                                        bookshelfState.searchBooks(
                                            value.isEmpty ? null : value);
                                      }
                                    },
                                  ),
                                )
                              : IconButton(
                                  key: const ValueKey('search-icon'),
                                  icon: const Icon(Icons.search),
                                  tooltip: 'Search books',
                                  onPressed: () {
                                    HapticFeedback.selectionClick();
                                    // Set up focus node before the state change
                                    final focusNode = FocusNode();
                                    setState(() {
                                      _showSearch = true;
                                    });

                                    // Schedule focus without using BuildContext across async gap
                                    WidgetsBinding.instance
                                        .addPostFrameCallback((_) {
                                      if (mounted) {
                                        focusNode.requestFocus();
                                      }
                                    });
                                  },
                                ),
                        ),
                    ],
                  ),
                  trailing: Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        IconButton(
                          alignment: Alignment.bottomLeft,
                          icon: const Icon(Icons.menu),
                          onPressed: () {
                            _expanded = !_expanded!;
                            setState(() {});
                          },
                        ),
                        SizedBox(height: DesignSystem.spaceL),
                      ],
                    ),
                  ),
                  extended: _expanded!,
                  selectedIndex: _currentIndex,
                  onDestinationSelected: (index) {
                    setState(() {
                      _currentIndex = index;
                      // Also sync the TabController for consistent state
                      _tabController.animateTo(index);

                      // Hide search when switching away from bookshelf tab
                      if (index != 0 && _showSearch) {
                        _showSearch = false;
                        _searchController.clear();
                        final bookshelfState = _getBookshelfState();
                        if (bookshelfState != null) {
                          bookshelfState.searchBooks(null);
                        }
                      }

                      // If switching to dictionary tab, we could add special handling here if needed
                    });
                  },
                  destinations: tabItems
                      .map((tab) => NavigationRailDestination(
                            icon: tab.icon!,
                            label: Text(tab.text!),
                          ))
                      .toList(),
                  labelType: _expanded!
                      ? NavigationRailLabelType.none
                      : NavigationRailLabelType.all,
                  backgroundColor: ElevationOverlay.applySurfaceTint(
                      colorScheme.surface, colorScheme.primary, 1),
                ),
                Expanded(child: pages[_currentIndex]),
              ],
            ),
          );
        }
        // For mobile/smaller tablets
        else {
          return Scaffold(
            body: NestedScrollView(
              headerSliverBuilder: (context, innerBoxIsScrolled) {
                return [
                  SliverAppBar(
                    pinned: true, // Keep this to ensure AppBar stays visible
                    floating: false,
                    snap: false,
                    forceElevated: innerBoxIsScrolled,
                    // Fixed height properties - adjusted to 78dp as requested
                    toolbarHeight: 56.0,
                    expandedHeight: 78.0,
                    collapsedHeight: 78.0,
                    // Enhanced elevation for better depth perception
                    scrolledUnderElevation: DesignSystem.elevationM,
                    shadowColor: colorScheme.shadow,
                    surfaceTintColor: colorScheme.surfaceTint,
                    // Ensure the title is fixed and non-collapsing
                    titleSpacing: DesignSystem.spaceM,
                    automaticallyImplyLeading: false,
                    // Move the title content to flexibleSpace for proper handling
                    flexibleSpace: FlexibleSpaceBar(
                      titlePadding: EdgeInsets.zero,
                      // Ensure content is visible in collapsed state
                      expandedTitleScale: 1.0, // No scaling on expand/collapse
                      title: SafeArea(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            // Main AppBar row with avatar, title and search - no extra top padding
                            Padding(
                              padding:
                                  EdgeInsets.only(left: DesignSystem.spaceM),
                              child: Row(
                                children: [
                                  // Avatar on the left - Enlarged and styled
                                  Container(
                                    width: 48,
                                    height: 48,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: colorScheme.primaryContainer,
                                    ),
                                    child: Material(
                                      color: Colors.transparent,
                                      child: InkWell(
                                        onTap: _showSettings,
                                        customBorder: const CircleBorder(),
                                        child: Padding(
                                          padding: EdgeInsets.all(
                                              DesignSystem.spaceS + 4),
                                          child: Icon(
                                            Icons.person_outline,
                                            size: 24,
                                            color:
                                                colorScheme.onPrimaryContainer,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  // This expanded will push the title to center
                                  Expanded(
                                    child: _showSearch && _currentIndex == 0
                                        ? Padding(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: DesignSystem.spaceM,
                                                vertical: DesignSystem.spaceXS),
                                            child: SearchBar(
                                              controller: _searchController,
                                              hintText: L10n.of(context)
                                                  .search_books_hint,
                                              hintStyle:
                                                  WidgetStateProperty.all(
                                                TextStyle(
                                                    color: colorScheme
                                                        .onSurfaceVariant),
                                              ),
                                              textStyle:
                                                  WidgetStateProperty.all(
                                                TextStyle(
                                                    color:
                                                        colorScheme.onSurface),
                                              ),
                                              padding:
                                                  const WidgetStatePropertyAll<
                                                      EdgeInsets>(
                                                EdgeInsets.symmetric(
                                                    horizontal: 16.0),
                                              ),
                                              elevation:
                                                  const WidgetStatePropertyAll<
                                                      double>(0),
                                              backgroundColor:
                                                  WidgetStateProperty
                                                      .resolveWith<Color>(
                                                (states) {
                                                  if (states.contains(
                                                      WidgetState.focused)) {
                                                    return colorScheme
                                                        .surfaceContainerLow;
                                                  }
                                                  return Colors.transparent;
                                                },
                                              ),
                                              leading: Icon(
                                                Icons.search,
                                                color: colorScheme
                                                    .onSurfaceVariant,
                                                size: 20,
                                              ),
                                              trailing: [
                                                if (_searchController
                                                    .text.isNotEmpty)
                                                  IconButton(
                                                    icon: Icon(
                                                      Icons.close,
                                                      color: colorScheme
                                                          .onSurfaceVariant,
                                                      size: 20,
                                                    ),
                                                    onPressed: () {
                                                      _searchController.clear();
                                                      final bookshelfState =
                                                          _getBookshelfState();
                                                      if (bookshelfState !=
                                                          null) {
                                                        bookshelfState
                                                            .searchBooks(null);
                                                      }
                                                    },
                                                  ),
                                              ],
                                              onChanged: (value) {
                                                final bookshelfState =
                                                    _getBookshelfState();
                                                if (bookshelfState != null) {
                                                  bookshelfState.searchBooks(
                                                      value.isEmpty
                                                          ? null
                                                          : value);
                                                }
                                              },
                                            ),
                                          )
                                        : Center(
                                            child: Text(
                                              "DassoShu",
                                              semanticsLabel:
                                                  "DassoShu Reader App",
                                              style: TextStyle(
                                                fontSize: 20,
                                                fontWeight: FontWeight.bold,
                                                color: colorScheme.onSurface,
                                                letterSpacing: 0.5,
                                              ),
                                            ),
                                          ),
                                  ),

                                  // This ensures balanced spacing on the right side
                                  if (_currentIndex == 0 && !_showSearch)
                                    IconButton(
                                      icon: const Icon(Icons.search),
                                      tooltip: 'Search books',
                                      constraints: const BoxConstraints(
                                          minWidth: 48, minHeight: 48),
                                      onPressed: () {
                                        // Set up focus node before the state change
                                        final focusNode = FocusNode();
                                        setState(() {
                                          _showSearch = true;
                                        });

                                        // Schedule focus using proper pattern to avoid BuildContext across async gap
                                        WidgetsBinding.instance
                                            .addPostFrameCallback((_) {
                                          if (mounted) {
                                            focusNode.requestFocus();
                                          }
                                        });

                                        // Add haptic feedback for better accessibility
                                        HapticFeedback.selectionClick();
                                      },
                                    )
                                  else if (_showSearch)
                                    IconButton(
                                      icon: const Icon(Icons.close),
                                      tooltip: 'Clear search',
                                      constraints: const BoxConstraints(
                                          minWidth: 48, minHeight: 48),
                                      onPressed: () {
                                        setState(() {
                                          _showSearch = false;
                                          _searchController.clear();
                                          if (_currentIndex == 0) {
                                            final bookshelfState =
                                                _getBookshelfState();
                                            if (bookshelfState != null) {
                                              bookshelfState.searchBooks(null);
                                            }
                                          }
                                        });
                                        // Add haptic feedback for better accessibility
                                        HapticFeedback.selectionClick();
                                      },
                                    )
                                  else
                                    // Add an invisible spacer of same width as the icon button
                                    // to balance the layout when no icon is showing
                                    SizedBox(width: 48.0),
                                ],
                              ),
                            ),
                            // Minimal spacing between AppBar and TabBar
                            SizedBox(height: DesignSystem.spaceS),
                          ],
                        ),
                      ),
                    ),
                    title:
                        null, // Remove original title since we moved it to flexibleSpace
                    actions: const [], // Keep empty since actions are in the row
                    bottom: TabBar(
                      controller: _tabController,
                      tabs: tabItems.map((tab) {
                        final isSelected =
                            _tabController.index == tabItems.indexOf(tab);
                        return Tab(
                          height:
                              72, // Increased height for better touch targets
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              // Apply animation to the icon when selected
                              isSelected
                                  ? ShaderMask(
                                      blendMode: BlendMode.srcIn,
                                      shaderCallback: (bounds) =>
                                          LinearGradient(
                                        colors: [
                                          colorScheme.primary,
                                          colorScheme.tertiary
                                        ],
                                      ).createShader(bounds),
                                      child:
                                          tab.icon ?? const Icon(Icons.error),
                                    )
                                  : tab.icon ?? const SizedBox.shrink(),
                              SizedBox(
                                  height: DesignSystem
                                      .spaceXS), // Consistent spacing
                              // Text that adapts to available width
                              FittedBox(
                                fit: BoxFit.scaleDown,
                                child: Text(
                                  tab.text ?? '',
                                  maxLines: 1,
                                  style: TextStyle(
                                    fontSize: 12,
                                    letterSpacing:
                                        -0.2, // Slightly tighter letter spacing
                                    fontWeight: isSelected
                                        ? FontWeight.w600
                                        : FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                      isScrollable: false,
                      labelColor: colorScheme.primary,
                      unselectedLabelColor: colorScheme.onSurfaceVariant
                          .withAlpha(204), // 0.8 * 255 = 204
                      indicatorColor: colorScheme.primary,
                      indicatorWeight: 3.0,
                      indicator: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            color: colorScheme.primary,
                            width: 3.0,
                          ),
                        ),
                      ),
                      // Minimal padding to maximize space for tab content
                      padding: EdgeInsets.zero,
                      labelPadding: const EdgeInsets.symmetric(horizontal: 2.0),
                      tabAlignment: TabAlignment.fill,
                      splashBorderRadius:
                          BorderRadius.circular(DesignSystem.radiusM),
                      overlayColor:
                          WidgetStateProperty.resolveWith<Color?>((states) {
                        if (states.contains(WidgetState.hovered)) {
                          return colorScheme.primary
                              .withAlpha(20); // 0.08 * 255 = 20
                        }
                        if (states.contains(WidgetState.pressed)) {
                          return colorScheme.primary
                              .withAlpha(31); // 0.12 * 255 = 31
                        }
                        return null;
                      }),
                      // Add haptic feedback for tab selection
                      onTap: (index) {
                        if (_currentIndex != index) {
                          HapticFeedback.selectionClick();
                          setState(() {
                            _currentIndex = index;

                            // Hide search when switching away from bookshelf tab
                            if (index != 0 && _showSearch) {
                              _showSearch = false;
                              _searchController.clear();
                              final bookshelfState = _getBookshelfState();
                              if (bookshelfState != null) {
                                bookshelfState.searchBooks(null);
                              }
                            }
                          });
                        }
                      },
                    ),
                  ),
                ];
              },
              // Replace TabBarView with PageView for animated transitions
              body: TabBarView(
                controller: _tabController,
                children: pages,
              ),
            ),
            floatingActionButton: _currentIndex == 0
                ? Padding(
                    padding: EdgeInsets.only(
                        bottom: DesignSystem.spaceM,
                        right: DesignSystem.spaceM),
                    child: FloatingActionButton(
                      onPressed: () {
                        _showAddOptions();
                        // Add haptic feedback for better accessibility
                        HapticFeedback.mediumImpact();
                      },
                      elevation: DesignSystem.elevationM,
                      shape: const CircleBorder(),
                      // Add animation for FAB
                      heroTag: 'importBookFAB',
                      // Enhanced styling
                      backgroundColor: colorScheme.primaryContainer,
                      foregroundColor: colorScheme.onPrimaryContainer,
                      // Add tooltip for accessibility
                      tooltip: 'Add Book',
                      child: Semantics(
                        label: 'Add new book',
                        hint: 'Opens menu to add books',
                        button: true,
                        child: const Icon(Icons.add, size: 26),
                      ),
                    ),
                  )
                : null,
          );
        }
      },
    );
  }

  // Now update the getBookshelfState method to use the GlobalKey
  BookshelfPageState? _getBookshelfState() {
    return _bookshelfKey.currentState;
  }
}
