import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/models/hsk_character_set.dart';
import 'package:dasso_reader/page/home_page/hsk_page/hsk_learn_screen.dart';
import 'package:dasso_reader/page/home_page/hsk_page/hsk_practice_screen.dart';
import 'package:dasso_reader/page/home_page/hsk_page/hsk_review_screen.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class HskSetDetailsScreen extends ConsumerWidget {
  final HskCharacterSet characterSet;

  const HskSetDetailsScreen({
    super.key,
    required this.characterSet,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Calculate progress statistics
    final totalViews = characterSet.characters.fold<int>(
      0,
      (total, character) => total + character.viewCount,
    );

    final mastered = characterSet.characters.where((c) => c.isMastered).length;
    final progressPercentage = characterSet.characters.isEmpty
        ? 0
        : (mastered / characterSet.characters.length * 100).round();

    // Calculate individual mode progress statistics using available fields
    final learnProgress = characterSet.characters.isEmpty
        ? 0
        : (characterSet.characters.where((c) => c.learnCycleState > 0).length /
                characterSet.characters.length *
                100)
            .round();

    final practiceProgress = characterSet.characters.isEmpty
        ? 0
        : (characterSet.characters
                    .where((c) => c.practiceCorrectStreak > 0)
                    .length /
                characterSet.characters.length *
                100)
            .round();

    final reviewProgress = characterSet.characters.isEmpty
        ? 0
        : (characterSet.characters
                    .where((c) => c.lastPracticedCorrectly != null)
                    .length /
                characterSet.characters.length *
                100)
            .round();

    return Scaffold(
      appBar: AppBar(
        title: Text(
          "Characters ${characterSet.startId} - ${characterSet.endId}",
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.blue.shade900,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.blue.shade900,
              Colors.teal.shade700,
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: EdgeInsets.all(
                DesignSystem.spaceL + 4), // 24.0 (preserves exact spacing)
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Statistics Display
                Card(
                  color: Colors.black26,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(DesignSystem.radiusM -
                        4), // 12.0 (preserves exact radius)
                  ),
                  child: Padding(
                    padding: EdgeInsets.all(
                        DesignSystem.spaceM), // 16.0 (preserves exact spacing)
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              "Total character views:",
                              style: TextStyle(color: Colors.white70),
                            ),
                            Text(
                              "$totalViews",
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(
                            height: DesignSystem
                                .spaceS), // 8.0 (preserves exact spacing)
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              "Total time:",
                              style: TextStyle(color: Colors.white70),
                            ),
                            Text(
                              _formatTime(characterSet.totalViewTime),
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(
                            height: DesignSystem
                                .spaceS), // 8.0 (preserves exact spacing)

                        // Learn progress row
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              "Learn progress:",
                              style: TextStyle(color: Colors.white70),
                            ),
                            Text(
                              "$learnProgress%",
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(
                            height: DesignSystem
                                .spaceS), // 8.0 (preserves exact spacing)

                        // Practice progress row
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              "Practice progress:",
                              style: TextStyle(color: Colors.white70),
                            ),
                            Text(
                              "$practiceProgress%",
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(
                            height: DesignSystem
                                .spaceS), // 8.0 (preserves exact spacing)

                        // Review progress row
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              "Review progress:",
                              style: TextStyle(color: Colors.white70),
                            ),
                            Text(
                              "$reviewProgress%",
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(
                            height: DesignSystem
                                .spaceS), // 8.0 (preserves exact spacing)
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              "Total progress:",
                              style: TextStyle(color: Colors.white70),
                            ),
                            Text(
                              "$progressPercentage%",
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(
                            height: DesignSystem
                                .spaceM), // 16.0 (preserves exact spacing)
                        LinearProgressIndicator(
                          value: progressPercentage / 100,
                          backgroundColor: Colors.black38,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            progressPercentage == 100
                                ? Colors.green
                                : Colors.orange.shade300,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                SizedBox(
                    height: DesignSystem.spaceXL +
                        8), // 32.0 (preserves exact spacing)

                // Mode Buttons
                ModeButton(
                  title: "Learn",
                  icon: Icons.school,
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => HskLearnScreen(
                          characterSet: characterSet,
                        ),
                      ),
                    );
                  },
                ),

                SizedBox(
                    height:
                        DesignSystem.spaceM), // 16.0 (preserves exact spacing)

                ModeButton(
                  title: "Practice",
                  icon: Icons.quiz,
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => HskPracticeScreen(
                          characterSet: characterSet,
                        ),
                      ),
                    );
                  },
                ),

                SizedBox(
                    height:
                        DesignSystem.spaceM), // 16.0 (preserves exact spacing)

                ModeButton(
                  title: "Review",
                  icon: Icons.history_edu,
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => HskReviewScreen(
                          characterSet: characterSet,
                        ),
                      ),
                    );
                  },
                ),

                const Spacer(),

                // Mountain silhouette decoration (similar to home screen)
                Container(
                  height: 100,
                  width: double.infinity,
                  alignment: Alignment.bottomCenter,
                  child: CustomPaint(
                    size: const Size(double.infinity, 80),
                    painter: MountainPainter(),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatTime(int seconds) {
    if (seconds < 60) {
      return "$seconds sec";
    } else if (seconds < 3600) {
      final minutes = seconds ~/ 60;
      return "$minutes min";
    } else {
      final hours = seconds ~/ 3600;
      final minutes = (seconds % 3600) ~/ 60;
      return "$hours h $minutes min";
    }
  }
}

class ModeButton extends StatelessWidget {
  final String title;
  final IconData icon;
  final VoidCallback onPressed;

  const ModeButton({
    super.key,
    required this.title,
    required this.icon,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.grey.shade800,
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(
            vertical: DesignSystem.spaceM), // 16.0 (preserves exact spacing)
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
              DesignSystem.radiusM - 4), // 12.0 (preserves exact radius)
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon),
          SizedBox(
              width: DesignSystem.spaceM - 4), // 12.0 (preserves exact spacing)
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}

// Simple painter to create a mountain silhouette effect (same as in home screen)
class MountainPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.2)
      ..style = PaintingStyle.fill;

    final path = Path();

    // Start at bottom left
    path.moveTo(0, size.height);

    // First mountain
    path.lineTo(size.width * 0.2, size.height * 0.4);

    // Second mountain
    path.lineTo(size.width * 0.35, size.height * 0.7);

    // Third mountain
    path.lineTo(size.width * 0.5, size.height * 0.2);

    // Fourth mountain
    path.lineTo(size.width * 0.7, size.height * 0.6);

    // Fifth mountain
    path.lineTo(size.width * 0.85, size.height * 0.3);

    // End at bottom right
    path.lineTo(size.width, size.height);

    // Close the path
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
